package viclass.editor.geo.impl.constructor.angle

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLine
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Angle
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.doc.ParamExtractorManager
import viclass.editor.geo.impl.elements.AngleImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.extractors.LineExtractor
import viclass.editor.geo.impl.extractors.PointExtractor
import kotlin.reflect.KClass

/**
 *
 * <AUTHOR>
 */
@Singleton
class AngleEC constructor(
    val extractorManager: ParamExtractorManager,
    val pointExtractor: PointExtractor,
    val lineExtractor: LineExtractor,
) : ElementConstructor<Angle> {

    override fun outputType(): KClass<Angle> {
        return Angle::class
    }

    private enum class CGS {
        FromLinesAndDirection,
        FromThreePoints
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromLinesAndDirection.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLine]!!,
                        listOf("Line"),
                        "tpl-LineStart"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aLine]!!,
                        listOf("Line"),
                        "tpl-LineEnd"
                    )
                    .constraintDepends(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0),
                        listOf("Value"),
                        "tpl-DirectionOfLineStart"
                    )
                    .constraintDepends(
                        3, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(1),
                        listOf("Value"),
                        "tpl-DirectionOfLineEnd"
                    )
                    .build(),
            )
            .elTypes(Angle::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Angle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromLinesAndDirection -> {
                Validations.validateNumConstraints(c, 4)
                constructFromLineAndDirection(doc, inputName, c)
            }

            CGS.FromThreePoints -> {
                Validations.validateNumConstraints(c, 4)
                constructFromThreePointsAngleName(doc, inputName, c)
            }
        }
    }

    private fun constructFromLineAndDirection(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Angle> {
        val exr1: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<LineVi> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3: NumberExtraction<Int> = extractFirstPossible(doc, ParamKind.PK_Value, c.params[2], c.ctIdx)
        val exr4: NumberExtraction<Int> = extractFirstPossible(doc, ParamKind.PK_Value, c.params[3], c.ctIdx)

        val lineStart: LineVi = exr1.result.result()!!
        val lineEnd: LineVi = exr2.result.result()!!
        val directionStart: Int = exr3.result
        val directionEnd: Int = exr4.result

        val anglePointV = Intersections.of(lineStart, lineEnd) ?: throw ConstructionException("Invalid angle")
        val anglePointName: String = inputName?.let {
            val pointNames = NamePattern.extractPointName(Angle::class, it)
            if (pointNames.size == 3) {
                pointNames[1]
            } else if (pointNames.size == 1) {
                pointNames[0]
            } else {
                throw ConstructionException(
                    "Invalid input name for angle: $it"
                )
            }
        } ?: generatePointName(doc)
        val anglePoint: Point = PointImpl(doc, anglePointName, anglePointV.x, anglePointV.y, anglePointV.z)

        val lineStartName = lineStart.name?.let { NamePattern.extractPointName(LineVi::class, it) }
        val lineEndName = lineEnd.name?.let { NamePattern.extractPointName(LineVi::class, it) }

        val angleName = (lineStartName?.getOrNull(directionStart) ?: lineStartName?.get(0)) + anglePoint.name +
                (lineEndName?.getOrNull(directionEnd) ?: lineEndName?.get(0))
        val angle: Angle =
            AngleImpl(doc, angleName, anglePoint, lineStart, directionStart, lineEnd, directionEnd)

        val cr = ConstructionResultImpl<Angle>()
        cr.setResult(angle)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.addDependency(anglePoint, listOf(lineStart, lineEnd), true)

        return cr
    }

    private fun constructFromThreePointsAngleName(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Angle> {
        val cr = ConstructionResultImpl<Angle>()

        val exr1: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[0], c.ctIdx)
        val exr2: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[1], c.ctIdx)
        val exr3: ElementExtraction<Point> = extractFirstPossible(doc, ParamKind.PK_Name, c.params[2], c.ctIdx)
        val exr4: NumberListExtraction<Int> = extractFirstPossible(doc, ParamKind.PK_Value, c.params[3], c.ctIdx)

        val point1 = exr1.result.result()!!
        val point2 = exr2.result.result()!!
        val point3 = exr3.result.result()!!
        val direction = exr4.result

        val startLineName = point2.name + point1.name
        val endLineName = point2.name + point3.name

        val lineStartExr = lineExtractor.extractConstructionResult(doc, startLineName, c.ctIdx, LineSegment::class)
        val lineEndExr = lineExtractor.extractConstructionResult(doc, endLineName, c.ctIdx, LineSegment::class)
        val anglePointName = point2.name

        val anglePoint = pointExtractor.extractConstructionResult<Point>(doc, anglePointName!!, c.ctIdx)
            .result() ?: throw ElementNotExistInDocumentException("angle point not found")
        val angle: Angle = AngleImpl(
            doc,
            inputName,
            anglePoint,
            lineStartExr.result()!!,
            direction[0],
            lineEndExr.result()!!,
            direction[1]
        )

        cr.setResult(angle)
        cr.mergeAsDependency(exr1.result)
        cr.mergeAsDependency(exr2.result)
        cr.mergeAsDependency(exr3.result)
        cr.mergeAsDependency(lineStartExr)
        cr.mergeAsDependency(lineEndExr)

        return cr
    }
}
