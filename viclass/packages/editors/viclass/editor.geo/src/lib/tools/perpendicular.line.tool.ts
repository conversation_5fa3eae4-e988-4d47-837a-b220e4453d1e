import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { ThenSelector, vert } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { buildPointConstruction, requestElementNames, pickPointName } from './tool.utils';
import {
    performConstructionWithVertex,
    createLineToolSelLogic,
    handleLineToolPointerEvent,
} from './parallel_perpendicular.line.tool.utils';

/**
 * Perpendicular Line Tool - Creates perpendicular lines using selector pattern
 * <AUTHOR>
 */
export class CreatePerpendicularLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreatePerpendicularLineTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;
    isShowingPreview = false;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedPoint = undefined;
        this.previewLine = undefined;
        this.isShowingPreview = false;
        super.resetState();
    }

    /**
     * Creates the selection logic: select line first, then point, then show preview and select final vertex
     */
    private createSelLogic() {
        this.selLogic = createLineToolSelLogic(
            this.pQ,
            this.pointerHandler,
            async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const selected = selector.selected;
                if (!selected || selected.length < 3) return;

                this.selectedLine = selected[0] as RenderLine;
                this.selectedPoint = selected[1] as RenderVertex;
                // Extract vertex from either RenderVertex or VertexOnStroke using vert function
                const finalVertex = vert(selected[2] as any);
                // Keep the original selection for determining if it's on a stroke
                const finalVertexSelection = selected[2];

                // Use the utility function to perform construction based on final vertex position
                await this.performConstructionWithFinalVertex(doc, finalVertex, finalVertexSelection);
            }
        );
    }

    /**
     * Performs construction using the utility function based on final vertex selection
     */
    private async performConstructionWithFinalVertex(
        ctrl: GeoDocCtrl,
        finalVertex: RenderVertex,
        finalVertexSelection?: any
    ) {
        if (!this.selectedLine || !this.selectedPoint) return;

        // Check if final vertex needs construction (doesn't have a name and has negative relIndex)
        const needsVertexConstruction = !finalVertex.name && finalVertex.relIndex < 0;
        let vertexConstructions: GeoElConstructionRequest[] = [];

        if (needsVertexConstruction) {
            // Request name for the vertex
            const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
            const inputPointNames = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Điểm',
                        originElement: [finalVertex],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];

            if (!inputPointNames.length) {
                this.resetState();
                return;
            }

            // Assign name to the vertex
            finalVertex.name = inputPointNames[0];

            // Create vertex construction
            const vertexConstruction = buildPointConstruction(finalVertex.name, {
                x: finalVertex.coords[0],
                y: finalVertex.coords[1],
            });
            vertexConstructions.push(vertexConstruction);
        }

        // Use the new utility function to handle construction logic
        await performConstructionWithVertex(
            this,
            ctrl,
            {
                point: this.selectedPoint,
                line: this.selectedLine,
                finalVertex: finalVertex,
                finalVertexSelection: finalVertexSelection,
                buildLineSegmentWithIntersectLine: this.buildLineSegmentWithIntersectLine.bind(this),
                buildLineSegment: this.buildLineSegment.bind(this),
                buildLine: this.buildLine.bind(this),
                vertexConstructions: vertexConstructions, // Pass vertex constructions
            },
            true // isPerpendicularVector = true for perpendicular lines
        );
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        return handleLineToolPointerEvent(
            this,
            event,
            this.selLogic,
            this.pQ,
            -31, // Preview ID for perpendicular lines
            true, // isPerpendicularVector = true
            {
                selectedLine: this.selectedLine,
                selectedPoint: this.selectedPoint,
                previewLine: this.previewLine,
                isShowingPreview: this.isShowingPreview,
                setSelectedLine: line => {
                    this.selectedLine = line;
                },
                setSelectedPoint: point => {
                    this.selectedPoint = point;
                },
                setPreviewLine: line => {
                    this.previewLine = line;
                },
                setIsShowingPreview: showing => {
                    this.isShowingPreview = showing;
                },
            }
        );
    }

    private buildLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'ThoughPointPerpendicularWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegment(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string,
        k: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'PerpendicularWithNewPoint'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegmentWithIntersectLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        intersectionLineName: string,
        intersectionLineType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/PerpendicularWithOtherEC',
            'LineVi',
            'ThroughPointSegmentPerpendicularWithLineAndIntersectionLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-PerpendicularWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
