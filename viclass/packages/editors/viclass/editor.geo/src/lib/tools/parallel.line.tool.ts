import { vector } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption } from '@viclass/editor.core';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { syncRenderCommands } from '../cmd';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue, pLine } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { ThenSelector, vert, VertexOnStroke } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    buildPointConstruction,
    requestElementNames,
    pickPointName,
    projectPointOntoLine,
    isSamePoint,
    calculateScalingFactor,
    calculateUnitVector,
    addHistoryItemFromConstructionResponse,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
} from './tool.utils';
import { createLineToolSelLogic } from './parallel_perpendicular.line.tool.utils';

/**
 * Parallel Line Tool - Creates parallel lines using selector pattern
 * <AUTHOR>
 */
export class CreateParallelLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateParallelLineTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;
    isShowingPreview = false;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedPoint = undefined;
        this.previewLine = undefined;
        this.isShowingPreview = false;
        super.resetState();
    }

    /**
     * Creates the selection logic: select line first, then point, then show preview and select final vertex
     */
    private createSelLogic() {
        this.selLogic = createLineToolSelLogic(
            this.pQ,
            this.pointerHandler,
            async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const selected = selector.selected;
                if (!selected || selected.length < 3) return;

                this.selectedLine = selected[0] as RenderLine;
                this.selectedPoint = selected[1] as RenderVertex;
                // Extract vertex from either RenderVertex or VertexOnStroke using vert function
                const finalVertex = vert(selected[2] as RenderVertex | VertexOnStroke);
                // Keep the original selection for determining if it's on a stroke
                const finalVertexSelection = selected[2];

                // Use the utility function to perform construction based on final vertex position
                await this.performConstructionWithFinalVertex(
                    doc,
                    finalVertex,
                    finalVertexSelection as RenderVertex | VertexOnStroke
                );
            }
        );
    }

    /**
     * Performs construction for parallel line based on final vertex selection
     * Logic: After selecting line and first vertex, preview line is fixed at first vertex.
     * Then select second vertex:
     * - If second vertex is NOT on preview line -> use buildLine() (infinite line)
     * - If second vertex IS on preview line -> use buildLineSegment() (line segment)
     */
    private async performConstructionWithFinalVertex(
        ctrl: GeoDocCtrl,
        finalVertex: RenderVertex,
        finalVertexSelection?: RenderVertex | VertexOnStroke
    ) {
        if (!this.selectedLine || !this.selectedPoint) return;

        // Check if final vertex needs construction (doesn't have a name and has negative relIndex)
        const needsVertexConstruction = !finalVertex.name && finalVertex.relIndex < 0;
        let vertexConstructions: GeoElConstructionRequest[] = [];

        if (needsVertexConstruction) {
            // Request name for the vertex
            const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
            const inputPointNames = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Điểm',
                        originElement: [finalVertex],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];

            if (!inputPointNames.length) {
                this.resetState();
                return;
            }

            // Assign name to the vertex
            finalVertex.name = inputPointNames[0];

            // Create vertex construction
            const vertexConstruction = buildPointConstruction(finalVertex.name, {
                x: finalVertex.coords[0],
                y: finalVertex.coords[1],
            });
            vertexConstructions.push(vertexConstruction);
        }

        // Custom logic for parallel line construction
        await this.performParallelLineConstruction(ctrl, finalVertex, finalVertexSelection, vertexConstructions);
    }

    /**
     * Performs parallel line construction based on whether second vertex is on preview line
     */
    private async performParallelLineConstruction(
        ctrl: GeoDocCtrl,
        finalVertex: RenderVertex,
        finalVertexSelection: RenderVertex | VertexOnStroke,
        vertexConstructions: GeoElConstructionRequest[]
    ) {
        if (!this.selectedLine || !this.selectedPoint) return;

        const finalVertexCoords = finalVertex.coords;
        const startPointName = this.selectedPoint.name;
        const startLineName = this.selectedLine.name;
        const startLineType = this.selectedLine.elType;

        // Calculate parallel line vector (same as original line)
        const lineVector = this.selectedLine.vector;
        const startPoint = this.selectedPoint.coords;

        // Project final vertex onto the parallel line through selectedPoint
        const projectPoint = projectPointOntoLine(finalVertexCoords, startPoint, lineVector);

        // Check if final vertex is on the preview line (parallel line through selectedPoint)
        const isOnPreviewLine = isSamePoint(finalVertexCoords, projectPoint, ctrl);

        // Check if the final vertex is on another line (from VertexOnStroke selection)
        let intersectLine: RenderLine | undefined;
        if (finalVertexSelection && Array.isArray(finalVertexSelection) && finalVertexSelection.length === 2) {
            // This is a VertexOnStroke selection: [StrokeType, RenderVertex]
            const [stroke, _vertex] = finalVertexSelection;
            // Check if the stroke is not the preview line (preview lines have negative relIndex)
            if (stroke.relIndex >= 0 && isElementLine(stroke)) {
                intersectLine = stroke as RenderLine;
            }
        }

        // Calculate project point on intersect line if exists
        let projectPointIntersect: number[] | undefined;
        if (intersectLine) {
            const vI = intersectLine.vector;
            const v1 = [-vI[1], vI[0]];
            projectPointIntersect = projectPointOntoLine(finalVertexCoords, startPoint, v1);
        }

        // Logic conditions based on original implementation
        const haveIntersectLine = !!intersectLine;
        const isPointStartOnIntersectLine = projectPointIntersect
            ? isSamePoint(startPoint, projectPointIntersect, ctrl)
            : false;
        const isPointerAtStartPosition = isSamePoint(finalVertexCoords, startPoint, ctrl);
        const isPointerOnParallelLine = isOnPreviewLine;

        let constructionRequest: GeoElConstructionRequest;
        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;

        // Case 1: Selected 2nd line and start point is not on 2nd line (create line segment with intersection)
        if (haveIntersectLine && !isPointStartOnIntersectLine) {
            const inputPointNames = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Đoạn Thẳng',
                        originElement: [this.selectedPoint, finalVertex],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];

            if (!inputPointNames.length) {
                this.resetState();
                return;
            }

            constructionRequest = this.buildLineSegmentWithIntersectLine(
                inputPointNames.join(''),
                startLineName,
                startLineType,
                intersectLine!.name,
                intersectLine!.elType,
                startPointName
            );
        }
        // Case 2: Choose a point on the parallel line and the chosen point is not the starting point (create line segment)
        else if (!isPointerAtStartPosition && isPointerOnParallelLine) {
            const inputPointNames = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Đoạn Thẳng',
                        originElement: [this.selectedPoint, finalVertex],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];

            if (!inputPointNames.length) {
                this.resetState();
                return;
            }

            // Calculate scaling factor for line segment
            const uVector = calculateUnitVector(lineVector);
            const k = calculateScalingFactor(uVector, startPoint, projectPoint);
            const lVector = [projectPoint[0] - startPoint[0], projectPoint[1] - startPoint[1]];

            // Determine direction using vector library
            const lineDirection =
                vector(lineVector[0], lineVector[1]).angleTo(vector(lVector[0], lVector[1])) * (180 / Math.PI) == 180
                    ? -1
                    : 1;

            constructionRequest = this.buildLineSegment(
                inputPointNames.join(''),
                startLineName,
                startLineType,
                startPointName,
                k * lineDirection
            );
        }
        // Case 3: Create straight line
        else {
            constructionRequest = this.buildLine('', startLineName, startLineType, startPointName);
        }

        // Execute construction
        await this.executeConstruction(ctrl, constructionRequest, vertexConstructions);
    }

    /**
     * Helper method to execute construction with awareness
     */
    private async executeConstruction(
        ctrl: GeoDocCtrl,
        constructionRequest: GeoElConstructionRequest,
        vertexConstructions: GeoElConstructionRequest[]
    ) {
        this.resetState();

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo hình',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                // Prepare construction requests - include vertex constructions if any
                const constructionRequests = [];

                // Add vertex constructions first (if any)
                if (vertexConstructions && vertexConstructions.length > 0) {
                    vertexConstructions.forEach(vertexConstruction => {
                        constructionRequests.push({
                            construction: vertexConstruction,
                        });
                    });
                }

                // Add the main construction
                constructionRequests.push({
                    construction: constructionRequest,
                });

                const constructResponse = await this.editor.geoGateway.construct(
                    ctrl.state.globalId,
                    constructionRequests
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                this.resetState();
            }
        );
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, (event: any) =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        // Continue with selection logic
        const selected = this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (selected && selected.length === 1) {
            // Line selected, show initial preview
            this.selectedLine = selected[0] as RenderLine;
            this.showPreviewLineAfterLineSelection(ctrl);
        } else if (selected && selected.length === 2) {
            // Line and point selected, show preview through point and KEEP IT FIXED
            this.selectedLine = selected[0] as RenderLine;
            this.selectedPoint = selected[1] as RenderVertex;
            this.showFixedPreviewLine(ctrl);
        } else if (selected && selected.length === 3) {
            // All selections complete, keep the fixed preview line
            this.selectedLine = selected[0] as RenderLine;
            this.selectedPoint = selected[1] as RenderVertex;
            // Don't update preview line here - keep it fixed at selectedPoint
        }

        // Handle hover preview updates for final vertex selection
        if (selected && selected.length === 2 && this.isShowingPreview) {
            // Keep the preview line fixed at selectedPoint - don't update it during hover
            // The preview line should remain fixed to show where the parallel line will be
        }

        // Always flush at the end
        this.pQ.flush(ctrl);
    }

    /**
     * Shows initial preview line after selecting a line
     */
    private showPreviewLineAfterLineSelection(ctrl: GeoDocCtrl) {
        if (!this.selectedLine) return;

        // Calculate parallel line vector (same as original line)
        const lineVector = this.selectedLine.vector;
        if (!lineVector || lineVector.length < 2) return;

        // Get start and end coordinates of the selected line
        const startCoords = this.selectedLine.coord('start', ctrl.rendererCtrl);
        const endCoords = this.selectedLine.coord('end', ctrl.rendererCtrl);

        if (!startCoords || !endCoords || startCoords.length < 2 || endCoords.length < 2) return;

        // Create a preview line at the center of the selected line as a starting point
        const lineMidpoint = {
            coords: [
                (startCoords[0] + endCoords[0]) / 2,
                (startCoords[1] + endCoords[1]) / 2,
                (startCoords[2] || 0 + endCoords[2] || 0) / 2,
            ],
            name: 'temp_midpoint',
            relIndex: -33,
            usable: true,
            valid: true,
        } as RenderVertex;

        this.previewLine = this.createPreviewLine(ctrl, lineMidpoint, lineVector);
        if (this.previewLine) {
            this.pQ.add(this.previewLine);
        }
    }

    /**
     * Shows fixed preview line through the selected point (after line + point selection)
     */
    private showFixedPreviewLine(ctrl: GeoDocCtrl) {
        if (!this.selectedLine || !this.selectedPoint) return;

        // Calculate parallel line vector (same as original line)
        const lineVector = this.selectedLine.vector;

        // Create preview line through the selected point with parallel vector
        this.previewLine = this.createPreviewLine(ctrl, this.selectedPoint, lineVector);
        this.pQ.add(this.previewLine);
        this.isShowingPreview = true;
    }

    /**
     * Helper to create preview line
     */
    private createPreviewLine(ctrl: GeoDocCtrl, vertex: RenderVertex, lineVector: number[]) {
        return pLine(
            ctrl,
            -30, // Preview ID for parallel line
            RenderLine,
            vertex,
            undefined,
            lineVector
        );
    }

    private buildLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/ParallelWithOtherEC',
            'LineVi',
            'ThroughPointParallelWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-ParallelWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegment(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string,
        k: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/ParallelWithOtherEC',
            'LineVi',
            'ThroughPointSegmentParallelWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-ParallelWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegmentWithIntersectLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        intersectionLineName: string,
        intersectionLineType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/ParallelWithOtherEC',
            'LineVi',
            'ThroughPointSegmentParallelWithLineAndIntersectionLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-ParallelWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
