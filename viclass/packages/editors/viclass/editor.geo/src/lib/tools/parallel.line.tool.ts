import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderVertex } from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl } from '../objects';
import { or, stroke, then, ThenSelector, vertex, vertexOnStroke, vert } from '../selectors';
import { GeometryTool } from './geo.tool';
import {
    getFocusDocCtrl,
    handleIfPointerNotInError,
    buildPointConstruction,
    requestElementNames,
    pickPointName,
} from './tool.utils';
import { performConstructionWithVertex } from './parallel_perpendicular.line.tool.utils';
import { NamingElementTool } from './naming.element.tool';

/**
 * Parallel Line Tool - Creates parallel lines using selector pattern
 * <AUTHOR>
 */
export class CreateParallelLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateParallelLineTool';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;
    isShowingPreview = false;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedPoint = undefined;
        this.previewLine = undefined;
        this.isShowingPreview = false;
        super.resetState();
    }

    /**
     * Creates the selection logic: select line first, then point, then show preview and select final vertex
     */
    private createSelLogic() {
        // First select a line
        const lineSelector = stroke({
            selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Then select a point
        const pointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Finally select a vertex for construction - can be regular vertex or vertex on preview stroke
        const finalVertexSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                }),
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    preview: true, // Enable preview element selection
                    syncPreview: true,
                }),
            ],
            { flatten: true }
        );

        // Main selection logic: select line, then point, then show preview and select final vertex
        this.selLogic = then([lineSelector, pointSelector, finalVertexSelector], {
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const selected = selector.selected;
                if (!selected || selected.length < 3) return;

                this.selectedLine = selected[0] as RenderLine;
                this.selectedPoint = selected[1] as RenderVertex;
                // Extract vertex from either RenderVertex or VertexOnStroke using vert function
                const finalVertex = vert(selected[2] as any);
                // Keep the original selection for determining if it's on a stroke
                const finalVertexSelection = selected[2];

                // Use the utility function to perform construction based on final vertex position
                await this.performConstructionWithFinalVertex(doc, finalVertex, finalVertexSelection);
            },
        });
    }

    /**
     * Shows preview parallel line immediately after selecting a line, before selecting any point
     * This creates a preview line that will be updated as the user hovers over potential points
     */
    private showPreviewLineAfterLineSelection(ctrl: GeoDocCtrl) {
        if (!this.selectedLine) return;

        // Calculate parallel line vector (same as original line)
        const lineVector = this.selectedLine.vector;
        if (!lineVector || lineVector.length < 2) return;

        // Get start and end coordinates of the selected line
        const startCoords = this.selectedLine.coord('start', ctrl.rendererCtrl);
        const endCoords = this.selectedLine.coord('end', ctrl.rendererCtrl);

        if (!startCoords || !endCoords || startCoords.length < 2 || endCoords.length < 2) return;

        // Create a preview line at the center of the selected line as a starting point
        // This will be updated when the user hovers over points
        const lineMidpoint = {
            coords: [
                (startCoords[0] + endCoords[0]) / 2,
                (startCoords[1] + endCoords[1]) / 2,
                (startCoords[2] || 0 + endCoords[2] || 0) / 2, // Z coordinate
            ],
            name: 'temp_midpoint',
            relIndex: -33, // Unique preview ID for midpoint
            usable: true,
            valid: true,
        } as RenderVertex;

        this.previewLine = pLine(
            ctrl,
            -30, // Preview ID for parallel line
            RenderLine,
            lineMidpoint,
            undefined,
            lineVector
        );

        if (this.previewLine) {
            this.pQ.add(this.previewLine);
        }
    }

    /**
     * Shows preview of the parallel line after selecting line and point
     */
    private showPreviewLine(ctrl: GeoDocCtrl) {
        if (!this.selectedLine || !this.selectedPoint) return;

        // Calculate parallel line vector (same as original line)
        const lineVector = this.selectedLine.vector;

        // Create preview line through the selected point with parallel vector
        this.previewLine = pLine(
            ctrl,
            -30, // Preview ID for parallel line
            RenderLine,
            this.selectedPoint,
            undefined,
            lineVector
        );

        this.pQ.add(this.previewLine);
        this.isShowingPreview = true;
    }

    /**
     * Updates the preview line to go through a different vertex while keeping it parallel
     */
    private updatePreviewLineToVertex(ctrl: GeoDocCtrl, trialVertex: any) {
        if (!this.selectedLine || !trialVertex) return;

        // Extract the actual RenderVertex from the trial selection
        const vertex = vert(trialVertex);
        if (!vertex || !vertex.coords || vertex.coords.length < 2) return;

        // Calculate parallel line vector (same as original line)
        const lineVector = this.selectedLine.vector;
        if (!lineVector || lineVector.length < 2) return;

        // Create new preview line through the hovered vertex with parallel vector
        // Using the same ID will replace the existing preview line
        this.previewLine = pLine(
            ctrl,
            -30, // Preview ID for parallel line (same ID replaces existing)
            RenderLine,
            vertex,
            undefined,
            lineVector
        );

        this.pQ.add(this.previewLine);
    }

    /**
     * Performs construction using the utility function based on final vertex selection
     */
    private async performConstructionWithFinalVertex(
        ctrl: GeoDocCtrl,
        finalVertex: RenderVertex,
        finalVertexSelection?: any
    ) {
        if (!this.selectedLine || !this.selectedPoint) return;

        // Check if final vertex needs construction (doesn't have a name and has negative relIndex)
        const needsVertexConstruction = !finalVertex.name && finalVertex.relIndex < 0;
        let vertexConstructions: GeoElConstructionRequest[] = [];

        if (needsVertexConstruction) {
            // Request name for the vertex
            const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;
            const inputPointNames = (
                await requestElementNames(ctrl, nt, [
                    {
                        objName: 'Điểm',
                        originElement: [finalVertex],
                        pickName: pickPointName,
                        namesToAvoid: [],
                    },
                ])
            )[0];

            if (!inputPointNames.length) {
                this.resetState();
                return;
            }

            // Assign name to the vertex
            finalVertex.name = inputPointNames[0];

            // Create vertex construction
            const vertexConstruction = buildPointConstruction(finalVertex.name, {
                x: finalVertex.coords[0],
                y: finalVertex.coords[1],
            });
            vertexConstructions.push(vertexConstruction);
        }

        // Use the new utility function to handle construction logic
        await performConstructionWithVertex(
            this,
            ctrl,
            {
                point: this.selectedPoint,
                line: this.selectedLine,
                finalVertex: finalVertex,
                finalVertexSelection: finalVertexSelection,
                buildLineSegmentWithIntersectLine: this.buildLineSegmentWithIntersectLine.bind(this),
                buildLineSegment: this.buildLineSegment.bind(this),
                buildLine: this.buildLine.bind(this),
                vertexConstructions: vertexConstructions, // Pass vertex constructions
            },
            false // isPerpendicularVector = false for parallel lines
        );
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove') {
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        } else {
            this.doTrySelection(event, ctrl);
        }

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: any, ctrl: GeoDocCtrl) {
        // Continue with selection logic
        const selected = this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (selected && selected.length === 1) {
            // Line selected, show initial preview
            this.selectedLine = selected[0] as RenderLine;
            this.showPreviewLineAfterLineSelection(ctrl);
        } else if (selected && selected.length === 2) {
            // Line and point selected, show preview through point
            this.selectedLine = selected[0] as RenderLine;
            this.selectedPoint = selected[1] as RenderVertex;
            this.showPreviewLine(ctrl);
        } else if (selected && selected.length === 3) {
            // All selections complete, show final preview
            this.selectedLine = selected[0] as RenderLine;
            this.selectedPoint = selected[1] as RenderVertex;
            const finalVertex = vert(selected[2] as any);
            this.updatePreviewLineToVertex(ctrl, finalVertex);
        }

        // Handle hover preview updates for final vertex selection
        if (selected && selected.length === 2 && this.isShowingPreview) {
            // Check if we're currently on the third selector (final vertex selector)
            if (this.selLogic.selectors && this.selLogic.selectors.length > 2) {
                const finalSelector = this.selLogic.selectors[2]; // The third selector is finalVertexSelector (OrSelector)

                // Try to get trial selection from the final vertex selector
                const trialVertex = finalSelector?.tryPreview?.(event, ctrl);

                if (trialVertex) {
                    // Update preview line to go through the hovered vertex
                    this.updatePreviewLineToVertex(ctrl, trialVertex);
                }
            }
        }

        // Always flush at the end - this is the pattern used by other tools
        this.pQ.flush(ctrl);
    }

    private buildLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/ParallelWithOtherEC',
            'LineVi',
            'ThroughPointParallelWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-ParallelWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegment(
        name: string,
        lineStartName: string,
        lineStartType: string,
        throughPointName: string,
        k: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/ParallelWithOtherEC',
            'LineVi',
            'ThroughPointSegmentParallelWithLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-ParallelWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-2DPoint',
                params: {
                    value: {
                        type: 'singleValue',
                        value: k,
                    },
                },
            },
        ];

        return construction;
    }

    private buildLineSegmentWithIntersectLine(
        name: string,
        lineStartName: string,
        lineStartType: string,
        intersectionLineName: string,
        intersectionLineType: string,
        throughPointName: string
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'LineVi/ParallelWithOtherEC',
            'LineVi',
            'ThroughPointSegmentParallelWithLineAndIntersectionLine'
        );
        construction.name = name;

        construction.paramSpecs = [
            {
                indexInCG: 1,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-ParallelWith',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineStartName,
                    },
                },
                dataTypes: {
                    name: lineStartType,
                },
            },
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-ThroughPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: throughPointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aLine',
                optional: false,
                tplStrLangId: 'tpl-IntersectionLine',
                params: {
                    name: {
                        type: 'singleValue',
                        value: intersectionLineName,
                    },
                },
                dataTypes: {
                    name: intersectionLineType,
                },
            },
        ];

        return construction;
    }
}
