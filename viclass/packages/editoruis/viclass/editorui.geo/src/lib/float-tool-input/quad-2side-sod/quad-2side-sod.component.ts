import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy } from '@angular/core';
import { GeometryToolBar, GeometryToolType, QuadToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for display and edit the number of edges for CreateRegularPolygonTool
 */
@Component({
    templateUrl: './quad-2side-sod.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Quad2SideSoDComponent implements AfterViewInit, OnDestroy, ToolListenerHost {
    private toolListener: ToolListener;

    public sod$ = new BehaviorSubject<number>(0);

    constructor(
        changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {
        this.toolListener = new ToolListener(this, tooltype, changeDetectorRef);
    }

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    setSOD(value: number) {
        const ts = this.toolbar.toolState(this.tooltype) as QuadToolState;
        ts.drawMode = value;
        this.toolbar.update(this.tooltype, ts);
    }

    updateInputFromToolState() {
        const ts = this.toolbar.toolState(this.tooltype) as QuadToolState;

        this.sod$.next(ts.drawMode);
    }
}
